.hero-section {
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: var(--dark-gray);
}

.hero-slider {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  transition: opacity 1s ease-in-out, visibility 1s ease-in-out;
}

.hero-slide.active {
  opacity: 1;
  visibility: visible;
}

.hero-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.hero-slide .hero-content {
  display: none;
}

.hero-slide.active .hero-content {
  display: block;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: var(--color-light);
  max-width: 800px;
  padding: var(--spacing-lg);
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.3),
    rgba(0, 0, 0, 0.5)
  );
}

.hero-title {
  font-size: clamp(var(--font-size-4xl), 6vw, var(--font-size-5xl));
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  background: var(--gradient-light);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
  font-size: clamp(var(--font-size-lg), 2.5vw, var(--font-size-xl));
  margin-bottom: var(--spacing-xl);
  opacity: 0.9;
  font-weight: 300;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.hero-slider-controls {
  position: absolute;
  bottom: var(--spacing-lg);
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  background: rgba(0, 0, 0, 0.3);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 50px;
  backdrop-filter: blur(5px);
}

.slider-control {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--color-light);
  transition: all 0.3s ease;
}

.slider-control:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.slider-dots {
  display: flex;
  gap: var(--spacing-xs);
}

.slider-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.slider-dot.active {
  background: var(--color-light);
  transform: scale(1.2);
}

.slider-dot:hover {
  background: rgba(255, 255, 255, 0.5);
}

@media (max-width: 768px) {
  .hero-slider-controls {
    bottom: var(--spacing-md);
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .slider-control {
    width: 35px;
    height: 35px;
  }

  .slider-dot {
    width: 8px;
    height: 8px;
  }
}
