/* Base Styles */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #e67e22;
    --text-color: #333;
    --light-gray: #f5f6fa;
    --white: #ffffff;
    --shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-gray);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Section */
.hero-section {
    height: 50vh;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease;
}

.hero-content p {
    font-size: 1.2rem;
    animation: fadeInUp 1s ease 0.2s;
    animation-fill-mode: both;
}

/* Categories Section */
.categories-section {
    padding: 60px 0;
    background-color: var(--white);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.category-card {
    position: relative;
    height: 200px;
    border-radius: 10px;
    overflow: hidden;
    text-decoration: none;
    color: var(--white);
    transition: var(--transition);
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.category-card:hover img {
    transform: scale(1.1);
}

.category-card h3 {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    margin: 0;
    font-size: 1.5rem;
}

/* Products Section */
.products-section {
    padding: 60px 0;
}

/* Filter Bar */
.filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box input {
    width: 100%;
    padding: 12px 40px 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 2px rgba(230, 126, 34, 0.1);
}

.search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.filter-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-options select {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: var(--transition);
    min-width: 150px;
}

.filter-options select:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 2px rgba(230, 126, 34, 0.1);
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

/* Product Card */
.product-card {
    background: var(--white);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.product-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--secondary-color);
    color: var(--white);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.product-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    opacity: 0;
    transform: translateX(20px);
    transition: var(--transition);
}

.product-card:hover .product-actions {
    opacity: 1;
    transform: translateX(0);
}

.action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--white);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    transition: var(--transition);
    box-shadow: var(--shadow);
}

.action-btn:hover {
    background: var(--secondary-color);
    color: var(--white);
}

.product-details {
    padding: 20px;
}

.product-details h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.product-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.product-price {
    margin-bottom: 15px;
}

.price {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-color);
}

.original-price {
    color: #999;
    text-decoration: line-through;
    margin-left: 10px;
    font-size: 0.9rem;
}

.product-rating {
    color: #ffc107;
    font-size: 0.9rem;
}

.product-rating span {
    color: #666;
    margin-left: 5px;
}

/* Quick View Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: var(--white);
    padding: 30px;
    border-radius: 10px;
    width: 90%;
    max-width: 900px;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    transition: var(--transition);
}

.close-modal:hover {
    color: var(--primary-color);
}

.quick-view-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.quick-view-image img {
    width: 100%;
    height: auto;
    border-radius: 5px;
}

.quick-view-details h2 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.8rem;
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border: 1px solid #ddd;
    background: var(--white);
    border-radius: 5px;
    cursor: pointer;
    transition: var(--transition);
}

.quantity-btn:hover {
    background: var(--light-gray);
}

.quantity-selector input {
    width: 50px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 5px;
}

.add-to-cart-btn {
    width: 100%;
    background-color: var(--secondary-color);
    color: var(--white);
    padding: 12px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: var(--transition);
}

.add-to-cart-btn:hover {
    background-color: #d35400;
}

/* Shopping Cart Sidebar */
.cart-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 100%;
    max-width: 400px;
    height: 100vh;
    background: var(--white);
    box-shadow: -2px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 1001;
    transition: var(--transition);
}

.cart-sidebar.show {
    right: 0;
}

.cart-header {
    padding: 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-header h3 {
    color: var(--primary-color);
    margin: 0;
}

.close-cart {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    transition: var(--transition);
}

.close-cart:hover {
    color: var(--primary-color);
}

.cart-items {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(100vh - 180px);
}

.cart-footer {
    padding: 20px;
    border-top: 1px solid #ddd;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--white);
}

.cart-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-size: 1.2rem;
    font-weight: 500;
}

.checkout-btn {
    width: 100%;
    background-color: var(--secondary-color);
    color: var(--white);
    padding: 12px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: var(--transition);
}

.checkout-btn:hover {
    background-color: #d35400;
}

.cart-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: none;
}

.cart-overlay.show {
    display: block;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero-content h1 {
        font-size: 2.8rem;
    }
    
    .quick-view-content {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .hero-section {
        height: 40vh;
    }
    
    .hero-content h1 {
        font-size: 2.2rem;
    }
    
    .filter-bar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-options {
        flex-direction: column;
    }
    
    .filter-options select {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 1.8rem;
    }
    
    .category-card {
        height: 150px;
    }
    
    .product-image {
        height: 200px;
    }
    
    .cart-sidebar {
        max-width: 100%;
    }
} 